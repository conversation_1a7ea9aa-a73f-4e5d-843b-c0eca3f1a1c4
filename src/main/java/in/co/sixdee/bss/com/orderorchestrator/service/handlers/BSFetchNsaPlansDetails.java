package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

@Log4j2
@Component(value = "fetchNsaPlans")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class BSFetchNsaPlansDetails extends AbstractDelegate {

    @Override
    protected void execute() throws Exception {

        String request = getRequestFromSpec();
        if (executionContext.isError())
            return;

        var callThirdPartyDTO = callThirdParty(request);
        if (callThirdPartyDTO == null) {
            executionContext.setError(true);
            return;
        }
        var response = callThirdPartyDTO.getResponse();
        validateResponse(callThirdPartyDTO);
        if (executionContext.isError())
            return;
        checkIfNsaPlanActivated(response);
        modifyWorkflowData(response);
        workflowDataUpdated = true;
        log.info("Received order from the message in fetch nsa plan :: {} ", objectMapper.writeValueAsString(executionContext));

    }

    protected void checkIfNsaPlanActivated(String response) throws JsonProcessingException {
        execution.setVariable("cancelNsaAddon", false);
        execution.setVariable("activateSaAddon", false);
        String nsaPlanIds = getNsaPlanIdFromConfig(executionContext.getEntityId());
        if (StringUtils.isNotEmpty(nsaPlanIds)) {
            List<String> nsaPlans = Arrays.asList(nsaPlanIds.split(","));
            List<Subscription> bsSubscriptions;
            JsonNode billResp = objectMapper.readTree(response);
            if (ObjectUtils.isNotEmpty(billResp.get("data")) && ObjectUtils.isNotEmpty(billResp.get("data").get(0))) {
                bsSubscriptions = objectMapper.readValue(objectMapper.writeValueAsString(billResp.get("data")), new TypeReference<List<Subscription>>() {
                });
                for (Subscription subscription : bsSubscriptions) {
                    if (StringUtils.isNotEmpty(subscription.getPlanId()) && StringUtils.isNotEmpty(subscription.getSubscriptionId()) && nsaPlans.contains(subscription.getPlanId())) {
                        log.info("Activated NSA plan in the bss system is {}", subscription.getPlanId());
                        executionContext.getObjectAttributes().put("nsaSubscriptionDetails",subscription);
                        execution.setVariable("cancelNsaAddon", true);
                        execution.setVariable("activateSaAddon", true);
                        break;
                    }
                }
            }
        }
    }

    protected String getNsaPlanIdFromConfig(String entityId) {
        String nsaPlanIds = null;
        CacheTableDataDTO planMappingConfig = cache.getCacheDetailsFromDBMap(
                CacheConstants.CacheKeys.COM_NSA_SA_PLANID_MAPPING_CONFIG.toString(), entityId);
        if (ObjectUtils.isNotEmpty(planMappingConfig)) {
            nsaPlanIds = planMappingConfig.getNgTableData().get("NSA_PLAN_ID");
        }
        return nsaPlanIds;
    }
}
