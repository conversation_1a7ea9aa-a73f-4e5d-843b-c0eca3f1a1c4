package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import java.math.BigDecimal;
import java.util.*;

import com.networknt.schema.ExecutionContext;

import in.co.sixdee.bss.common.jolt.JoltUtils;
import in.co.sixdee.bss.om.model.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.common.util.CommonUtils;
import in.co.sixdee.bss.om.model.dto.order.*;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
public class BSFetchAccountDetailsTest {

    @InjectMocks
    private BSFetchAccountDetails bsFetchAccountDetails;

    @Mock
    private JoltUtils joltUtils;

    @Mock
    private DelegateExecution execution;

    @Mock
    private CallThirdPartyDTO callThirdPartyDTO;

    @Spy
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        ReflectionTestUtils.setField(bsFetchAccountDetails, "execution", execution);
        ReflectionTestUtils.setField(bsFetchAccountDetails, "joltUtils", joltUtils);
        ReflectionTestUtils.setField(bsFetchAccountDetails, "objectMapper", new ObjectMapper());
        ReflectionTestUtils.setField(bsFetchAccountDetails, "reqSpecKey", "tpRequestSpec");
    }

    @Test
    void testExecute_ValidResponseWithExcessAmountAndOneActiveService() throws Exception {
        OrderFlowContext context = new OrderFlowContext();
        Order order = new Order();
        OrderPayment payment = new OrderPayment();
        payment.setTotalAmount("200");
        order.setPayment(payment);
        order.setOrderType("POSTPAID");
        context.setOrder(order);
        context.setAttributes(new LinkedHashMap<>());
        context.setErrorDetail(new OrderFlowContext.ErrorDetail());
        ReflectionTestUtils.setField(bsFetchAccountDetails, "executionContext", context);

        // Mock request generation
        when(joltUtils.convert(anyString(), any(), any(), any())).thenReturn("{\"req\":\"value\"}");

        // Valid billing response with 1 active service and outstandingBalance = 100
        String billingResponse = """
            {
              "data": [
                {
                  "accountBalanceDetails": {
                    "outstandingBalance": 100
                  },
                  "serviceDetails": [
                    {
                      "status": "1",
                      "networkServiceId": "1",
                      "ocsServiceSeqId": "SEQ123",
                      "serviceId": "SVC1"
                    }
                  ]
                }
              ]
            }
        """;
        when(callThirdPartyDTO.getResponse()).thenReturn(billingResponse);
        doReturn(callThirdPartyDTO).when(bsFetchAccountDetails).callThirdParty(anyString());

        // Execute
        bsFetchAccountDetails.execute();

        // Assert
        assertEquals("100", context.getAttributes().get("adjustmentAmount"));
        assertEquals("SEQ123", context.getAttributes().get("ocsServiceSeqId"));
        assertEquals("SVC1", context.getAttributes().get("serviceId"));
        verify(execution).setVariable("nccCreditAdjustment", true);
    }

    @Test
    void testExecute_NoExcessPayment() throws Exception {
        OrderFlowContext context = new OrderFlowContext();
        Order order = new Order();
        OrderPayment payment = new OrderPayment();
        payment.setTotalAmount("50");
        order.setPayment(payment);
        order.setOrderType("POSTPAID");
        context.setOrder(order);
        context.setAttributes(new LinkedHashMap<>());
        context.setErrorDetail(new OrderFlowContext.ErrorDetail());
        ReflectionTestUtils.setField(bsFetchAccountDetails, "executionContext", context);

        when(joltUtils.convert(anyString(), any(), any(), any())).thenReturn("{}");

        String response = """
            {
              "data": [
                {
                  "accountBalanceDetails": {
                    "outstandingBalance": 100
                  },
                  "serviceDetails": [
                    {
                      "status": "1",
                      "networkServiceId": "1",
                      "ocsServiceSeqId": "SEQ123",
                      "serviceId": "SVC1"
                    }
                  ]
                }
              ]
            }
        """;
        when(callThirdPartyDTO.getResponse()).thenReturn(response);
        doReturn(callThirdPartyDTO).when(bsFetchAccountDetails).callThirdParty(anyString());

        bsFetchAccountDetails.execute();

        verify(execution).setVariable("nccCreditAdjustment", false);
        assertNull(context.getAttributes().get("adjustmentAmount"));
    }

    @Test
    void testExecute_MultipleActiveServices() throws Exception {
        OrderFlowContext context = new OrderFlowContext();
        Order order = new Order();
        OrderPayment payment = new OrderPayment();
        payment.setTotalAmount("200");
        order.setPayment(payment);
        order.setOrderType("POSTPAID");
        context.setOrder(order);
        context.setAttributes(new LinkedHashMap<>());
        context.setErrorDetail(new OrderFlowContext.ErrorDetail());
        ReflectionTestUtils.setField(bsFetchAccountDetails, "executionContext", context);

        when(joltUtils.convert(anyString(), any(), any(), any())).thenReturn("{}");

        String response = """
            {
              "data": [
                {
                  "accountBalanceDetails": {
                    "outstandingBalance": 50
                  },
                  "serviceDetails": [
                    {
                      "status": "1",
                      "networkServiceId": "1",
                      "ocsServiceSeqId": "SEQ1",
                      "serviceId": "SVC1"
                    },
                    {
                      "status": "1",
                      "networkServiceId": "1",
                      "ocsServiceSeqId": "SEQ2",
                      "serviceId": "SVC2"
                    }
                  ]
                }
              ]
            }
        """;
        when(callThirdPartyDTO.getResponse()).thenReturn(response);
        doReturn(callThirdPartyDTO).when(bsFetchAccountDetails).callThirdParty(anyString());

        bsFetchAccountDetails.execute();

        verify(execution).setVariable("nccCreditAdjustment", false);
    }

    @Test
    void testExecute_InvalidJsonResponse() throws Exception {
        OrderFlowContext context = new OrderFlowContext();
        Order order = new Order();
        OrderPayment payment = new OrderPayment();
        payment.setTotalAmount("200");
        order.setPayment(payment);
        order.setOrderType("POSTPAID");
        context.setOrder(order);
        context.setAttributes(new LinkedHashMap<>());
        context.setErrorDetail(new OrderFlowContext.ErrorDetail());
        ReflectionTestUtils.setField(bsFetchAccountDetails, "executionContext", context);

        when(joltUtils.convert(anyString(), any(), any(), any())).thenReturn("{}");

        when(callThirdPartyDTO.getResponse()).thenReturn("invalid-json");
        doReturn(callThirdPartyDTO).when(bsFetchAccountDetails).callThirdParty(anyString());

        bsFetchAccountDetails.execute();

        verify(execution).setVariable("nccCreditAdjustment", false);
    }

    @Test
    void testExecute_JoltThrowsException() throws Exception {
        OrderFlowContext context = new OrderFlowContext();
        Order order = new Order();
        OrderPayment payment = new OrderPayment();
        payment.setTotalAmount("100");
        order.setPayment(payment);
        order.setOrderType("POSTPAID");
        context.setOrder(order);
        context.setAttributes(new LinkedHashMap<>());
        OrderFlowContext.ErrorDetail errorDetail = new OrderFlowContext.ErrorDetail();
        context.setErrorDetail(errorDetail);
        ReflectionTestUtils.setField(bsFetchAccountDetails, "executionContext", context);

        when(joltUtils.convert(anyString(), any(), any(), any()))
                .thenThrow(new RuntimeException("JOLT conversion error"));

        bsFetchAccountDetails.execute();

        assertTrue(context.isError());
        assertEquals("COM-001", context.getErrorDetail().getCode());
        verify(execution, never()).setVariable(eq("nccCreditAdjustment"), any());
    }
}
