package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.ProcessVariableUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.EnrichmentFailedException;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderEnrichment;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderStatusManager;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Cart;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.ServiceManagement;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ChangePlanServiceTest {

    @InjectMocks
    protected ChangePlanService changePlanService;

    @Mock
    protected RuntimeService runtimeService;

    @Mock
    protected OrderEnrichment orderEnrichment;

    @Mock
    protected OrderStatusManager orderStatusManager;

    @Mock
    protected ProcessVariableUtils processVariableUtils;

    @Mock
    protected DelegateExecution execution;

    private OrderFlowContext executionContext;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        executionContext = new OrderFlowContext();
        changePlanService.executionContext = executionContext;
        changePlanService.execution = execution;
    }

    @Test
    void testExecute_success() throws Exception {
        // Setup mock context
        LinkedHashMap<String, String> attributes = new LinkedHashMap<>();
        attributes.put("CHANGEPLAN_ORDER_ID", "order123");
        attributes.put("CHANGEPLAN_SUB_ORDER_ID", "suborder123");
        attributes.put("PROFILE_ID", "profile123");
        attributes.put("ACCOUNT_ID", "account123");

        Order order = new Order();
        ServiceManagement sm = new ServiceManagement();
        sm.setDestinationProfileId("profile123");
        sm.setDestinationAccountId("account123");
        sm.setSubscriptions(new ArrayList<>());

        order.setServiceManagement(sm);
        executionContext.setOrder(order);
        executionContext.setAttributes(attributes);

        when(execution.getVariable("priority")).thenReturn("high");
        when(processVariableUtils.createWorkflowData(any())).thenReturn(new LinkedHashMap<>());
        doNothing().when(processVariableUtils).findChangeSubscriptionProcessVariables(any(), any());

        // Run
        changePlanService.execute();

        // Verify
        verify(orderStatusManager).updateOrderParams("order123", "profile123", "account123");
        verify(orderEnrichment).enrichOrder(any());
        verify(runtimeService).startProcessInstanceByKey(eq("ChangeSubscription"), eq("order123"), any(Map.class));
    }

    @Test
    void testExecute_enrichmentFailed() throws Exception {
        // Setup
        executionContext.setOrder(new Order());
        executionContext.setAttributes(new LinkedHashMap<>());
        executionContext.setEnrichmentError(true);
        executionContext.setErrorCode("ERR123");
        executionContext.setEnrichmentFailureReason("Failed");

        assertThrows(EnrichmentFailedException.class, () -> changePlanService.execute());
    }
}
